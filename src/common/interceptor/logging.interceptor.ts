import { CallHand<PERSON>, ExecutionContext, Injectable, NestInterceptor, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Request, Response } from 'express';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();

    const { method, originalUrl, ip, headers, body, query } = request;
    const userAgent = headers['user-agent'] || '';
    const contentType = headers['content-type'] || '';

    // 获取用户信息（如果已登录）
    const user = (request as any).user;
    const userId = user?.user?.userId || 'anonymous';
    const userName = user?.user?.userName || 'anonymous';

    const startTime = Date.now();

    // 记录请求日志
    this.logger.log(`📥 [REQUEST] ${method} ${originalUrl} - User: ${userName}(${userId}) - IP: ${ip} - UA: ${userAgent}`);

    // 记录请求参数（排除敏感信息）
    if (Object.keys(query).length > 0) {
      this.logger.log(`📋 [QUERY] ${JSON.stringify(query)}`);
    }

    if (body && Object.keys(body).length > 0) {
      const sanitizedBody = this.sanitizeBody(body);
      this.logger.log(`📋 [BODY] ${JSON.stringify(sanitizedBody)}`);
    }

    return next.handle().pipe(
      tap((data) => {
        const endTime = Date.now();
        const duration = endTime - startTime;
        const statusCode = response.statusCode;

        // 记录响应日志
        this.logger.log(`📤 [RESPONSE] ${method} ${originalUrl} - ${statusCode} - ${duration}ms - User: ${userName}(${userId})`);

        // 记录响应数据（可选，根据需要开启）
        if (process.env.NODE_ENV === 'development') {
          const sanitizedData = this.sanitizeResponse(data);
          this.logger.debug(`📋 [RESPONSE_DATA] ${JSON.stringify(sanitizedData)}`);
        }
      }),
      catchError((error) => {
        const endTime = Date.now();
        const duration = endTime - startTime;

        // 记录错误日志
        this.logger.error(`❌ [ERROR] ${method} ${originalUrl} - ${duration}ms - User: ${userName}(${userId}) - Error: ${error.message}`);

        // 记录错误详情
        if (error.response) {
          this.logger.error(`📋 [ERROR_DETAIL] ${JSON.stringify(error.response)}`);
        }

        throw error;
      }),
    );
  }

  /**
   * 清理请求体中的敏感信息
   */
  private sanitizeBody(body: any): any {
    if (!body || typeof body !== 'object') {
      return body;
    }

    const sensitiveFields = ['password', 'token', 'secret', 'key', 'authorization'];
    const sanitized = { ...body };

    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '***';
      }
    }

    return sanitized;
  }

  /**
   * 清理响应数据中的敏感信息
   */
  private sanitizeResponse(data: any): any {
    if (!data || typeof data !== 'object') {
      return data;
    }

    // 如果响应数据太大，只记录基本信息
    const dataStr = JSON.stringify(data);
    if (dataStr.length > 1000) {
      return {
        code: data.code,
        msg: data.msg,
        dataSize: `${Math.round(dataStr.length / 1024)}KB`,
        hasData: !!data.data,
      };
    }

    return data;
  }
}
