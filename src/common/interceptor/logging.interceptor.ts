import { Call<PERSON><PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Request, Response } from 'express';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();

    const { method, originalUrl, ip } = request;
    const user = (request as any).user;
    const userName = user?.user?.userName || 'anonymous';
    const startTime = Date.now();

    return next.handle().pipe(
      tap(() => {
        const duration = Date.now() - startTime;
        const statusCode = response.statusCode;
        this.logger.log(`${method} ${originalUrl} - ${statusCode} - ${duration}ms - ${userName} - ${ip}`);
      }),
      catchError((error) => {
        const duration = Date.now() - startTime;
        this.logger.error(`${method} ${originalUrl} - ERROR - ${duration}ms - ${userName} - ${error.message}`);
        throw error;
      }),
    );
  }
}
